<script setup lang="ts">
import { ref } from 'vue';
import { supabase } from '../../lib/supabase';

const exporting = ref(false);
const error = ref('');

const exportData = async () => {
  try {
    exporting.value = true;
    error.value = '';
    
    // Get current user
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) {
      throw new Error('You must be logged in to export data');
    }
    
    // Get all analyses
    const { data: analyses, error: analysesError } = await supabase
      .from('analyses')
      .select('*')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: false });
    
    if (analysesError) throw analysesError;
    
    // Get all messages
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('*')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: true });
    
    if (messagesError) throw messagesError;
    
    // Prepare data structure
    const exportData = {
      profile: {
        id: userData.user.id,
        email: userData.user.email,
        created_at: userData.user.created_at
      },
      analyses: analyses || [],
      messages: messages || [],
      exported_at: new Date().toISOString()
    };
    
    // Convert to JSON and create download link
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileName = `convertiq-data-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileName);
    linkElement.click();
  } catch (e) {
    console.error('Error exporting data:', e);
    error.value = e.message || 'Failed to export data';
  } finally {
    exporting.value = false;
  }
};
</script>

<template>
  <div>
    <button
      @click="exportData"
      :disabled="exporting"
      class="px-4 py-2 bg-secondary-500 text-white rounded hover:bg-secondary-600 transition-colors disabled:opacity-50"
    >
      <span v-if="exporting" class="flex items-center">
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Exporting...
      </span>
      <span v-else>Export All Data</span>
    </button>
    
    <p v-if="error" class="mt-2 text-sm text-error-500">{{ error }}</p>
  </div>
</template>