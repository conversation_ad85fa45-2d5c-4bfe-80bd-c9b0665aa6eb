---
import DashboardLayout from '../../../layouts/DashboardLayout.astro';
import AnalysisResults from '../../../components/analysis/AnalysisResults.vue';
import WebsiteChat from '../../../components/chat/WebsiteChat.vue';

const { id } = Astro.params;
---

<DashboardLayout title="Analysis Results - ConvertIQ">
  <div class="max-w-5xl mx-auto">
    <AnalysisResults id={id} client:load />
    <div class="mt-8">
      <h2 class="text-xl font-bold mb-4">Chat about this analysis</h2>
      <WebsiteChat analysisId={id} client:load />
    </div>
  </div>
</DashboardLayout>