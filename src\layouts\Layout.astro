---
import '../styles/global.css';

interface Props {
  title: string;
  description?: string;
  isAuthenticated?: boolean;
}

const { 
  title, 
  description = "ConvertIQ - AI-powered landing page analysis and optimization",
  isAuthenticated = false
} = Astro.props;
---

<!DOCTYPE html>
<html lang="en" class="h-full">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <meta name="description" content={description} />
    <title>{title}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
  </head>
  <body class="min-h-full bg-gray-50 font-sans text-gray-900">
    {isAuthenticated ? (
      <div class="flex h-screen overflow-hidden">
        <slot />
      </div>
    ) : (
      <slot />
    )}
  </body>
</html>