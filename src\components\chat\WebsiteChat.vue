<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { supabase } from '../../lib/supabase';
import { chatWithAI } from '../../lib/openrouter';
import type { AnalysisWithMetadata, Message } from '../../types/analysis';

const props = defineProps<{
  analysisId: string;
}>();

const analysis = ref<AnalysisWithMetadata | null>(null);
const messages = ref<Message[]>([]);
const newMessage = ref('');
const loading = ref(true);
const sending = ref(false);
const error = ref('');
const messageEnd = ref<HTMLElement | null>(null);

onMounted(async () => {
  await Promise.all([
    loadAnalysis(),
    loadMessages()
  ]);
  
  scrollToBottom();
});

watch(() => messages.value.length, () => {
  scrollToBottom();
});

const loadAnalysis = async () => {
  try {
    const { data, error: fetchError } = await supabase
      .from('analyses')
      .select('*')
      .eq('id', props.analysisId)
      .single();
    
    if (fetchError) throw fetchError;
    
    analysis.value = data;
  } catch (e) {
    console.error('Error loading analysis:', e);
    error.value = 'Failed to load analysis data';
  } finally {
    loading.value = false;
  }
};

const loadMessages = async () => {
  try {
    const { data, error: fetchError } = await supabase
      .from('messages')
      .select('*')
      .eq('analysis_id', props.analysisId)
      .order('created_at', { ascending: true });
    
    if (fetchError) throw fetchError;
    
    messages.value = data || [];
    
    // If no messages, add a welcome message
    if (data.length === 0 && analysis.value) {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData?.user) return;
      
      const welcomeMessage = `Hi! I've analyzed ${analysis.value.url} and found some interesting insights. The website scored ${analysis.value.score}/10 overall. What would you like to know more about? I can discuss SEO improvements, content suggestions, user experience enhancements, or technical optimizations.`;
      
      const { data: msgData, error: msgError } = await supabase
        .from('messages')
        .insert({
          analysis_id: props.analysisId,
          user_id: userData.user.id,
          content: welcomeMessage,
          role: 'assistant'
        })
        .select()
        .single();
      
      if (msgError) throw msgError;
      
      messages.value = [msgData];
    }
  } catch (e) {
    console.error('Error loading messages:', e);
    error.value = 'Failed to load chat messages';
  }
};

const scrollToBottom = () => {
  setTimeout(() => {
    messageEnd.value?.scrollIntoView({ behavior: 'smooth' });
  }, 100);
};

const sendMessage = async () => {
  if (!newMessage.value.trim() || sending.value || !analysis.value) return;
  
  try {
    sending.value = true;
    
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return;
    
    // Save user message
    const { data: userMsgData, error: userMsgError } = await supabase
      .from('messages')
      .insert({
        analysis_id: props.analysisId,
        user_id: userData.user.id,
        content: newMessage.value,
        role: 'user'
      })
      .select()
      .single();
    
    if (userMsgError) throw userMsgError;
    
    messages.value.push(userMsgData);
    
    // Clear input
    const userQuery = newMessage.value;
    newMessage.value = '';
    
    // Format messages for AI
    const chatMessages = messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
    
    // Get AI response
    const websiteContext = {
      url: analysis.value.url,
      analysis: {
        seoScore: analysis.value.analysis_data?.seoScore || 5,
        contentQuality: analysis.value.analysis_data?.contentQuality || 5,
        userExperience: analysis.value.analysis_data?.userExperience || 5,
        technicalScore: analysis.value.analysis_data?.technicalScore || 5,
        overallScore: analysis.value.score,
        seoSuggestions: analysis.value.recommendations.slice(0, 3),
        contentSuggestions: analysis.value.recommendations.slice(3, 6),
        uxSuggestions: analysis.value.recommendations.slice(6, 9),
        technicalSuggestions: analysis.value.recommendations.slice(9),
        summary: analysis.value.analysis_data?.summary || 'Website analysis completed',
        strengths: analysis.value.pros,
        weaknesses: analysis.value.cons
      },
      content: analysis.value.analysis_data?.scrapedData?.content || ''
    };
    
    const aiResponse = await chatWithAI(chatMessages, websiteContext);
    
    // Save AI response
    const { data: aiMsgData, error: aiMsgError } = await supabase
      .from('messages')
      .insert({
        analysis_id: props.analysisId,
        user_id: userData.user.id,
        content: aiResponse,
        role: 'assistant'
      })
      .select()
      .single();
    
    if (aiMsgError) throw aiMsgError;
    
    messages.value.push(aiMsgData);
    
  } catch (e) {
    console.error('Error sending message:', e);
    error.value = 'Failed to send message. Please try again.';
  } finally {
    sending.value = false;
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(date);
};
</script>

<template>
  <div class="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col h-[600px]">
    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Chat About This Analysis</h2>
      <p v-if="analysis" class="text-sm text-gray-600 mt-1">
        Discussing: {{ analysis.title || analysis.url }}
      </p>
    </div>
    
    <div v-if="loading" class="flex-1 p-4 flex items-center justify-center">
      <div class="animate-pulse flex space-x-4">
        <div class="flex-1 space-y-4 py-1">
          <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          <div class="space-y-2">
            <div class="h-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else-if="error" class="flex-1 p-4 flex items-center justify-center">
      <div class="text-red-500 text-center">
        <svg class="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p>{{ error }}</p>
      </div>
    </div>
    
    <div v-else class="flex-1 p-4 overflow-y-auto" id="chat-messages">
      <div v-for="message in messages" :key="message.id" class="mb-6">
        <div 
          :class="[
            'max-w-[85%] rounded-lg p-4', 
            message.role === 'user' 
              ? 'bg-blue-600 text-white ml-auto' 
              : 'bg-gray-100 text-gray-800'
          ]"
        >
          <div class="whitespace-pre-wrap leading-relaxed">{{ message.content }}</div>
          <div 
            :class="[
              'text-xs mt-2 text-right', 
              message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
            ]"
          >
            {{ formatDate(message.created_at) }}
          </div>
        </div>
      </div>
      
      <div v-if="sending" class="max-w-[85%] bg-gray-100 text-gray-800 rounded-lg p-4 mb-6 animate-pulse">
        <div class="flex items-center space-x-2">
          <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></div>
          <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
          <span class="text-sm text-gray-500 ml-2">AI is thinking...</span>
        </div>
      </div>
      
      <div ref="messageEnd"></div>
    </div>
    
    <div class="border-t border-gray-200 p-4">
      <form @submit.prevent="sendMessage" class="flex items-end space-x-2">
        <div class="flex-1">
          <textarea
            v-model="newMessage"
            placeholder="Ask about SEO, content, UX, technical aspects, or any specific improvements..."
            class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            rows="2"
            :disabled="sending"
            @keydown.enter.exact.prevent="sendMessage"
            @keydown.enter.shift.exact="newMessage += '\n'"
          ></textarea>
        </div>
        <button
          type="submit"
          :disabled="!newMessage.trim() || sending"
          class="bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 transition-colors flex-shrink-0"
        >
          <svg v-if="sending" class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </form>
      
      <div class="mt-2 text-xs text-gray-500">
        Press Enter to send, Shift+Enter for new line
      </div>
    </div>
  </div>
</template>