---
import Layout from '../../layouts/Layout.astro';
import AnalysisResults from '../../components/analysis/AnalysisResults.vue';
import ChatPanel from '../../components/chat/ChatPanel.vue';

const mockAnalysis = {
  id: "test-analysis-1",
  url: "https://example.com/landing-page",
  title: "Example Landing Page",
  score: 7.5,
  conversion_rate: 3.2,
  pros: [
    "Clear value proposition",
    "Mobile responsive design",
    "Fast loading speed",
    "Professional visuals"
  ],
  cons: [
    "Limited social proof",
    "Form has too many fields",
    "Call to action not prominent enough",
    "Missing trust indicators"
  ],
  recommendations: [
    "Add customer testimonials to build trust",
    "Simplify the signup form to reduce friction",
    "Make the primary CTA button more prominent",
    "Include security badges and certifications"
  ],
  target_audience: "Small business owners and entrepreneurs looking for marketing solutions",
  adaptations: [
    "Customize messaging for enterprise clients",
    "Add specific examples for different industries",
    "Include ROI calculator for budget-conscious users"
  ],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

const mockMessages = [
  {
    id: "msg-1",
    content: "Hi there! I've analyzed your landing page. The page scored 7.5/10 with an estimated conversion rate of 3.2%. What would you like to know more about?",
    role: "assistant",
    created_at: new Date(Date.now() - 3600000).toISOString()
  },
  {
    id: "msg-2",
    content: "Can you explain why the conversion rate is only 3.2%?",
    role: "user",
    created_at: new Date(Date.now() - 3300000).toISOString()
  },
  {
    id: "msg-3",
    content: "The 3.2% conversion rate estimate is based on several factors I identified. The main areas affecting conversion are the lengthy form fields and limited social proof. However, this is actually within industry average for B2B services. To improve this, I recommend simplifying your form to just essential fields and adding customer testimonials prominently on the page.",
    role: "assistant",
    created_at: new Date(Date.now() - 3000000).toISOString()
  }
];
---

<Layout title="Test Dashboard - ConvertIQ">
  <div class="min-h-screen bg-gray-50">
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <span class="text-xl font-bold text-primary-500">ConvertIQ Test</span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <main class="py-10">
      <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow rounded-lg p-6 mb-8">
          <h1 class="text-2xl font-bold text-gray-900 mb-4">Test Dashboard</h1>
          <p class="text-gray-600">This is a test dashboard with mock data for UI/UX development.</p>
        </div>

        <div class="space-y-8">
          <AnalysisResults id="test-analysis-1" client:load />
          
          <div>
            <h2 class="text-xl font-bold mb-4">Chat Test</h2>
            <ChatPanel analysisId="test-analysis-1" client:load />
          </div>
        </div>
      </div>
    </main>
  </div>
</Layout>

<script>
  // Mock the Supabase client responses for the test page
  window.mockSupabase = {
    analysis: mockAnalysis,
    messages: mockMessages
  };
</script>