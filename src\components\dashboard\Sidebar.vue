<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../lib/supabase';

const analyses = ref([]);
const loading = ref(true);
const searchQuery = ref('');

onMounted(async () => {
  await loadAnalyses();
});

const loadAnalyses = async () => {
  try {
    loading.value = true;
    
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return;
    
    const { data, error } = await supabase
      .from('analyses')
      .select('id, url, title, score, created_at')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    analyses.value = data || [];
  } catch (error) {
    console.error('Error loading analyses:', error);
  } finally {
    loading.value = false;
  }
};

const filteredAnalyses = computed(() => {
  if (!searchQuery.value.trim()) return analyses.value;
  
  const query = searchQuery.value.toLowerCase();
  return analyses.value.filter(analysis => 
    analysis.title.toLowerCase().includes(query) || 
    analysis.url.toLowerCase().includes(query)
  );
});

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric' 
  }).format(date);
};
</script>

<template>
  <aside class="w-64 flex-shrink-0 bg-white border-r border-gray-200 overflow-y-auto">
    <div class="p-4">
      <div class="mb-6">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search analyses..."
            class="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
        </div>
      </div>
      
      <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Your Analyses</h3>
      
      <div v-if="loading" class="py-2">
        <div v-for="i in 5" :key="i" class="animate-pulse mb-2">
          <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          <div class="h-3 bg-gray-200 rounded w-1/2 mt-2"></div>
        </div>
      </div>
      
      <div v-else-if="filteredAnalyses.length === 0" class="py-4 text-center text-gray-500">
        <p v-if="searchQuery" class="text-sm">No analyses match your search</p>
        <p v-else class="text-sm">No analyses yet. Start by creating a new analysis.</p>
      </div>
      
      <ul v-else class="space-y-1">
        <li v-for="analysis in filteredAnalyses" :key="analysis.id">
          <a 
            :href="`/dashboard/analysis/${analysis.id}`"
            class="block px-3 py-2 text-sm rounded-md hover:bg-gray-100 transition-colors"
          >
            <div class="flex items-center justify-between">
              <span class="font-medium truncate">{{ analysis.title || analysis.url }}</span>
              <span class="ml-2 text-xs bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded">{{ analysis.score }}/10</span>
            </div>
            <div class="mt-1 flex items-center text-xs text-gray-500">
              <span class="truncate">{{ formatDate(analysis.created_at) }}</span>
            </div>
          </a>
        </li>
      </ul>
    </div>
  </aside>
</template>