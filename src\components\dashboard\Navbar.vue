<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';

const user = ref(null);
const profileMenuOpen = ref(false);
const loading = ref(true);

onMounted(async () => {
  try {
    const { data } = await supabase.auth.getUser();
    if (data?.user) {
      user.value = data.user;
      
      // Get profile data
      const { data: profileData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();
      
      if (profileData) {
        user.value = {
          ...user.value,
          ...profileData
        };
      }
    }
  } catch (error) {
    console.error('Error loading user:', error);
  } finally {
    loading.value = false;
  }
});

const toggleProfileMenu = () => {
  profileMenuOpen.value = !profileMenuOpen.value;
};

const signOut = async () => {
  try {
    await supabase.auth.signOut();
    window.location.href = '/';
  } catch (error) {
    console.error('Error signing out:', error);
  }
};

// Close menu when clicking outside
onMounted(() => {
  const handleClickOutside = (event) => {
    if (profileMenuOpen.value && !event.target.closest('.profile-menu-container')) {
      profileMenuOpen.value = false;
    }
  };
  
  document.addEventListener('click', handleClickOutside);
  
  return () => {
    document.removeEventListener('click', handleClickOutside);
  };
});
</script>

<template>
  <header class="bg-white shadow-sm z-10">
    <div class="flex items-center justify-between px-6 py-3">
      <div class="flex items-center">
        <a href="/dashboard" class="flex items-center">
          <img src="/logo.svg" alt="ConvertIQ Logo" class="h-8 w-auto mr-2">
          <span class="text-xl font-bold text-primary-500">ConvertIQ</span>
        </a>
      </div>
      
      <div class="flex items-center">
        <a 
          href="/dashboard/analysis/new" 
          class="mr-6 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        >
          <svg class="mr-2 -ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          New Analysis
        </a>
        
        <div class="relative profile-menu-container">
          <button 
            @click="toggleProfileMenu"
            class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <div v-if="loading" class="h-8 w-8 rounded-full bg-gray-200 animate-pulse"></div>
            <div v-else-if="user?.avatar_url" class="h-8 w-8 rounded-full overflow-hidden">
              <img :src="user.avatar_url" alt="User avatar" class="h-full w-full object-cover">
            </div>
            <div v-else class="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center text-white font-medium">
              {{ user?.email?.charAt(0).toUpperCase() || 'U' }}
            </div>
          </button>
          
          <div v-if="profileMenuOpen" class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 transition-all transform duration-150">
            <div class="py-1">
              <a href="/dashboard/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
              <button @click="signOut" class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>