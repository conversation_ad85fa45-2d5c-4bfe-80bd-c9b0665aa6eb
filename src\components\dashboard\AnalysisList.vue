<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import type { AnalysisWithMetadata } from '../../types/analysis';

const analyses = ref<AnalysisWithMetadata[]>([]);
const loading = ref(true);

onMounted(async () => {
  await loadAnalyses();
});

const loadAnalyses = async () => {
  try {
    loading.value = true;
    
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return;
    
    const { data, error } = await supabase
      .from('analyses')
      .select('*')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: false })
      .limit(6);
    
    if (error) throw error;
    
    analyses.value = data || [];
  } catch (error) {
    console.error('Error loading analyses:', error);
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric' 
  }).format(date);
};

const getScoreColor = (score) => {
  if (score >= 8) return 'text-success-500';
  if (score >= 6) return 'text-warning-500';
  return 'text-error-500';
};
</script>

<template>
  <div class="bg-white rounded-lg shadow overflow-hidden">
    <div v-if="loading" class="p-6">
      <div class="animate-pulse space-y-4">
        <div v-for="i in 4" :key="i" class="flex items-start">
          <div class="w-12 h-12 bg-gray-200 rounded-md"></div>
          <div class="ml-4 flex-1">
            <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else-if="analyses.length === 0" class="p-6 text-center">
      <p class="text-gray-500">You haven't created any analyses yet.</p>
      <a 
        href="/dashboard/analysis/new" 
        class="mt-4 inline-block px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors"
      >
        Create your first analysis
      </a>
    </div>
    
    <ul v-else class="divide-y divide-gray-200">
      <li v-for="analysis in analyses" :key="analysis.id">
        <a :href="`/dashboard/analysis/${analysis.id}`" class="block hover:bg-gray-50 transition-colors">
          <div class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <h3 class="text-lg font-medium text-gray-900 truncate">
                    {{ analysis.title || analysis.url }}
                  </h3>
                  <span 
                    class="ml-2 px-2 py-0.5 text-xs font-medium rounded-full"
                    :class="getScoreColor(analysis.score)"
                  >
                    {{ analysis.score }}/10
                  </span>
                </div>
                <p class="mt-1 text-sm text-gray-500 truncate">
                  {{ analysis.url }}
                </p>
              </div>
              <div class="flex flex-col items-end">
                <span class="text-sm text-gray-500">{{ formatDate(analysis.created_at) }}</span>
                <span class="mt-1 text-sm font-medium">
                  Est. conversion: {{ analysis.conversion_rate }}%
                </span>
              </div>
            </div>
            <div class="mt-3 flex flex-wrap gap-2">
              <span 
                v-for="(rec, index) in analysis.recommendations.slice(0, 2)" 
                :key="index"
                class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-secondary-500/10 text-secondary-700"
              >
                {{ rec.length > 40 ? rec.substring(0, 37) + '...' : rec }}
              </span>
              <span v-if="analysis.recommendations.length > 2" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                +{{ analysis.recommendations.length - 2 }} more
              </span>
            </div>
          </div>
        </a>
      </li>
    </ul>
    
    <div v-if="analyses.length > 0" class="px-6 py-4 bg-gray-50 border-t border-gray-200">
      <a href="/dashboard/history" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
        View all analyses →
      </a>
    </div>
  </div>
</template>