---
import DashboardLayout from '../../layouts/DashboardLayout.astro';
---

<DashboardLayout title="Analysis History - ConvertIQ">
  <div class="max-w-6xl mx-auto">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Analysis History</h1>
      <p class="text-gray-600 mt-1">View and manage all your landing page analyses</p>
    </div>
    
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="relative">
              <input
                type="text"
                placeholder="Search analyses..."
                class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            <select class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
              <option>All Scores</option>
              <option>8-10 (Excellent)</option>
              <option>6-7 (Good)</option>
              <option>4-5 (Average)</option>
              <option>1-3 (Needs Work)</option>
            </select>
          </div>
          <a 
            href="/dashboard/analysis/new" 
            class="bg-primary-500 text-white px-4 py-2 rounded hover:bg-primary-600 transition-colors"
          >
            New Analysis
          </a>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Page
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Score
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Conversion Rate
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <!-- This will be populated by Vue component -->
            <tr>
              <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                <div class="animate-pulse">
                  <div class="h-4 bg-gray-200 rounded w-1/4 mx-auto mb-2"></div>
                  <div class="h-3 bg-gray-200 rounded w-1/6 mx-auto"></div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</DashboardLayout>