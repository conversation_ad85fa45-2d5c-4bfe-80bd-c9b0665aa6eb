---
import DashboardLayout from '../../../layouts/DashboardLayout.astro';
import NewAnalysisCard from '../../../components/analysis/NewAnalysisCard.vue';
---

<DashboardLayout title="New Analysis - ConvertIQ">
  <div class="max-w-2xl mx-auto">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">New Landing Page Analysis</h1>
      <p class="text-gray-600 mt-1">Enter a URL to get AI-powered insights and recommendations</p>
    </div>
    
    <NewAnalysisCard client:load />
    
    <div class="mt-8 bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Tips for Better Analysis</h2>
      <ul class="space-y-3 text-sm text-gray-600">
        <li class="flex items-start">
          <svg class="h-5 w-5 text-primary-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <span>Make sure the URL is publicly accessible (not behind a login)</span>
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 text-primary-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <span>Landing pages work best - avoid analyzing complex multi-page sites</span>
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 text-primary-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <span>Include the full URL with https:// for best results</span>
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 text-primary-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <span>Analysis typically takes 10-30 seconds depending on page complexity</span>
        </li>
      </ul>
    </div>
  </div>
</DashboardLayout>