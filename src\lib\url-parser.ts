export async function fetchLandingPageContent(url: string): Promise<string> {
  try {
    // Normalize first
    let normalizedUrl = url.trim();
    if (!/^https?:\/\//i.test(normalizedUrl)) {
      normalizedUrl = 'https://' + normalizedUrl;
    }

    console.log('Fetching content for URL:', normalizedUrl);

    const response = await fetch(`/api/fetch-url?url=${encodeURIComponent(normalizedUrl)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch (e) {
        throw new Error(`API returned ${response.status}: ${response.statusText}`);
      }

      throw new Error(errorData.error || `Failed to fetch URL: ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.html) {
      throw new Error('No HTML content received from API');
    }

    return data.html;
  } catch (error) {
    console.error('Error fetching landing page:', error);

    if (error instanceof TypeError && error.message.includes('Invalid URL')) {
      throw new Error('Please enter a valid URL (e.g., https://example.com)');
    }

    throw new Error(error instanceof Error ? error.message : 'Failed to fetch landing page content. Please check the URL and try again.');
  }
}
