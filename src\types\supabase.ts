export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          display_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          display_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          display_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      analyses: {
        Row: {
          id: string
          user_id: string
          url: string
          title: string
          score: number
          conversion_rate: number
          pros: Json
          cons: Json
          recommendations: Json
          target_audience: string
          adaptations: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          url: string
          title: string
          score: number
          conversion_rate: number
          pros: Json
          cons: Json
          recommendations: Json
          target_audience: string
          adaptations: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          url?: string
          title?: string
          score?: number
          conversion_rate?: number
          pros?: Json
          cons?: Json
          recommendations?: Json
          target_audience?: string
          adaptations?: Json
          created_at?: string
          updated_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          analysis_id: string
          user_id: string
          content: string
          role: string
          created_at: string
        }
        Insert: {
          id?: string
          analysis_id: string
          user_id: string
          content: string
          role: string
          created_at?: string
        }
        Update: {
          id?: string
          analysis_id?: string
          user_id?: string
          content?: string
          role?: string
          created_at?: string
        }
      }
    }
  }
}