/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e6edff',
          100: '#ccdaff',
          200: '#99b6ff',
          300: '#6691ff',
          400: '#336dff',
          500: '#3366FF',  // Primary
          600: '#2952cc',
          700: '#1f3d99',
          800: '#142966',
          900: '#0a1433',
        },
        secondary: {
          50: '#e6faff',
          100: '#ccf5ff',
          200: '#99ecff',
          300: '#66e2ff',
          400: '#33d9ff',
          500: '#00B8D9',  // Secondary
          600: '#0093ad',
          700: '#006e82',
          800: '#004a57',
          900: '#00252b',
        },
        accent: {
          50: '#efeeff',
          100: '#dedeff',
          200: '#bebeff',
          300: '#9d9dff',
          400: '#7d7dff',
          500: '#6554C0',  // Accent
          600: '#514399',
          700: '#3d3273',
          800: '#28224c',
          900: '#141126',
        },
        success: {
          500: '#36B37E',
        },
        warning: {
          500: '#FFAB00',
        },
        error: {
          500: '#FF5630',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '0': '0',
        '1': '0.25rem',  // 4px
        '2': '0.5rem',   // 8px
        '3': '0.75rem',  // 12px
        '4': '1rem',     // 16px
        '5': '1.25rem',  // 20px
        '6': '1.5rem',   // 24px
        '8': '2rem',     // 32px
        '10': '2.5rem',  // 40px
        '12': '3rem',    // 48px
        '16': '4rem',    // 64px
        '20': '5rem',    // 80px
        '24': '6rem',    // 96px
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}