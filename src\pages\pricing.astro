---
import Layout from '../layouts/Layout.astro';

const plans = [
  {
    name: "Free",
    price: "$0",
    period: "forever",
    description: "Perfect for getting started",
    features: [
      "5 landing page analyses per month",
      "Basic AI recommendations",
      "Chat support for each analysis",
      "Email support"
    ],
    cta: "Get Started",
    popular: false
  },
  {
    name: "Pro",
    price: "$29",
    period: "per month",
    description: "For growing businesses",
    features: [
      "Unlimited landing page analyses",
      "Advanced AI insights",
      "Priority chat support",
      "Export analysis reports",
      "Team collaboration (up to 5 users)",
      "Priority email support"
    ],
    cta: "Start Free Trial",
    popular: true
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "contact us",
    description: "For large organizations",
    features: [
      "Everything in Pro",
      "Custom AI model training",
      "White-label solution",
      "Dedicated account manager",
      "Custom integrations",
      "SLA guarantee"
    ],
    cta: "Contact Sales",
    popular: false
  }
];
---

<Layout title="Pricing - ConvertIQ">
  <div class="min-h-screen bg-gray-50">
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <a href="/" class="flex items-center">
              <img src="/logo.svg" alt="ConvertIQ Logo" class="h-8 w-auto mr-2">
              <span class="text-xl font-bold text-primary-500">ConvertIQ</span>
            </a>
          </div>
          <div class="flex items-center space-x-4">
            <a href="/dashboard" class="text-gray-700 hover:text-primary-500 font-medium">Dashboard</a>
            <a href="/login" class="bg-primary-500 text-white px-4 py-2 rounded hover:bg-primary-600">Sign In</a>
          </div>
        </div>
      </div>
    </header>

    <main class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold text-gray-900 mb-4">Simple, Transparent Pricing</h1>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Choose the plan that's right for your business. All plans include our core AI-powered analysis features.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => (
            <div class={`bg-white rounded-lg shadow-md overflow-hidden ${plan.popular ? 'ring-2 ring-primary-500 relative' : ''}`}>
              {plan.popular && (
                <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <span class="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
                </div>
              )}
              
              <div class="p-6">
                <h3 class="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <div class="mb-4">
                  <span class="text-4xl font-bold text-gray-900">{plan.price}</span>
                  <span class="text-gray-600 ml-2">{plan.period}</span>
                </div>
                <p class="text-gray-600 mb-6">{plan.description}</p>
                
                <ul class="space-y-3 mb-8">
                  {plan.features.map((feature) => (
                    <li class="flex items-start">
                      <svg class="h-5 w-5 text-success-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <span class="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <button class={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                  plan.popular 
                    ? 'bg-primary-500 text-white hover:bg-primary-600' 
                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                }`}>
                  {plan.cta}
                </button>
              </div>
            </div>
          ))}
        </div>

        <div class="bg-white rounded-lg shadow-md p-8 mb-16">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Frequently Asked Questions</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Can I change plans anytime?</h3>
              <p class="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Is there a free trial?</h3>
              <p class="text-gray-600">Yes, all paid plans come with a 14-day free trial. No credit card required to start.</p>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">What payment methods do you accept?</h3>
              <p class="text-gray-600">We accept all major credit cards, PayPal, and bank transfers for enterprise plans.</p>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Do you offer refunds?</h3>
              <p class="text-gray-600">Yes, we offer a 30-day money-back guarantee for all paid plans.</p>
            </div>
          </div>
        </div>

        <div class="text-center">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Ready to Get Started?</h2>
          <p class="text-gray-600 mb-8">Start optimizing your landing pages today with our AI-powered analysis.</p>
          <a href="/login" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-500 hover:bg-primary-600 transition-colors mr-4">
            Start Free Trial
          </a>
          <a href="/contact" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
            Contact Sales
          </a>
        </div>
      </div>
    </main>
  </div>
</Layout>