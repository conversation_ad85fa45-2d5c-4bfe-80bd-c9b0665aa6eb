import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ request, url }) => {
  try {
    const requestUrl = new URL(request.url);
    const allParams = Object.fromEntries(requestUrl.searchParams.entries());

    console.log('Test API - request.url:', request.url);
    console.log('Test API - url param:', url);
    console.log('Test API - url.searchParams:', url?.searchParams?.toString());
    console.log('Test API - search params:', requestUrl.searchParams.toString());
    console.log('Test API - all params:', allParams);
    
    return new Response(JSON.stringify({
      requestUrl: request.url,
      urlParam: url?.toString(),
      urlSearchParams: url?.searchParams?.toString(),
      searchParams: requestUrl.searchParams.toString(),
      allParams: allParams,
      message: 'Test API working'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error in test API:', error);
    return new Response(JSON.stringify({ 
      error: 'Test API error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
