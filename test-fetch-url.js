// Simple test script to test the fetch-url API endpoint

async function testFetchUrl() {
  const testUrl = 'https://example.com';
  const apiUrl = `http://localhost:4322/api/fetch-url?url=${encodeURIComponent(testUrl)}`;

  console.log('Testing fetch-url API with:', testUrl);
  console.log('API endpoint:', apiUrl);
  console.log('Encoded URL:', encodeURIComponent(testUrl));

  try {
    const response = await fetch(apiUrl);
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('Response data:', data);

    if (response.ok) {
      console.log('✅ API test successful!');
      console.log('HTML content length:', data.html ? data.html.length : 'No HTML');
    } else {
      console.log('❌ API test failed:', data.error);
    }
  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

// Test with POST method
async function testFetchUrlPost() {
  const testUrl = 'https://example.com';
  const apiUrl = `http://localhost:4322/api/fetch-url`;

  console.log('\nTesting fetch-url API with POST method:', testUrl);

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url: testUrl })
    });
    console.log('Response status:', response.status);

    const data = await response.json();
    console.log('Response data:', data);

    if (response.ok) {
      console.log('✅ POST API test successful!');
      console.log('HTML content length:', data.html ? data.html.length : 'No HTML');
    } else {
      console.log('❌ POST API test failed:', data.error);
    }
  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

// Test scrape-website API for comparison
async function testScrapeWebsite() {
  const testUrl = 'https://example.com';
  const apiUrl = `http://localhost:4322/api/scrape-website?url=${encodeURIComponent(testUrl)}`;

  console.log('\nTesting scrape-website API with:', testUrl);
  console.log('API endpoint:', apiUrl);

  try {
    const response = await fetch(apiUrl);
    console.log('Response status:', response.status);

    const data = await response.json();
    console.log('Response data keys:', Object.keys(data));

    if (response.ok) {
      console.log('✅ Scrape API test successful!');
      console.log('Content length:', data.content ? data.content.length : 'No content');
    } else {
      console.log('❌ Scrape API test failed:', data.error);
    }
  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

// Test without URL parameter
async function testMissingUrl() {
  const apiUrl = 'http://localhost:4322/api/fetch-url';
  
  console.log('\nTesting fetch-url API without URL parameter');
  
  try {
    const response = await fetch(apiUrl);
    console.log('Response status:', response.status);
    
    const data = await response.json();
    console.log('Response data:', data);
    
    if (response.status === 400 && data.error) {
      console.log('✅ Error handling test successful!');
    } else {
      console.log('❌ Error handling test failed');
    }
  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

// Run tests
async function runTests() {
  await testFetchUrl();
  await testFetchUrlPost();
  await testScrapeWebsite();
  await testMissingUrl();
}

runTests();
