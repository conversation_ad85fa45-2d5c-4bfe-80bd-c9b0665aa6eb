<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../lib/supabase';
import type { AnalysisWithMetadata } from '../../types/analysis';

const analyses = ref<AnalysisWithMetadata[]>([]);
const loading = ref(true);
const searchQuery = ref('');
const scoreFilter = ref('all');
const deleting = ref<string | null>(null);

onMounted(async () => {
  await loadAnalyses();
});

const loadAnalyses = async () => {
  try {
    loading.value = true;
    
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return;
    
    const { data, error } = await supabase
      .from('analyses')
      .select('*')
      .eq('user_id', userData.user.id)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    analyses.value = data || [];
  } catch (error) {
    console.error('Error loading analyses:', error);
  } finally {
    loading.value = false;
  }
};

const filteredAnalyses = computed(() => {
  let filtered = analyses.value;
  
  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(analysis => 
      analysis.title.toLowerCase().includes(query) || 
      analysis.url.toLowerCase().includes(query)
    );
  }
  
  // Filter by score
  if (scoreFilter.value !== 'all') {
    switch (scoreFilter.value) {
      case 'excellent':
        filtered = filtered.filter(a => a.score >= 8);
        break;
      case 'good':
        filtered = filtered.filter(a => a.score >= 6 && a.score < 8);
        break;
      case 'average':
        filtered = filtered.filter(a => a.score >= 4 && a.score < 6);
        break;
      case 'poor':
        filtered = filtered.filter(a => a.score < 4);
        break;
    }
  }
  
  return filtered;
});

const deleteAnalysis = async (id: string) => {
  if (!confirm('Are you sure you want to delete this analysis? This action cannot be undone.')) {
    return;
  }
  
  try {
    deleting.value = id;
    
    const { error } = await supabase
      .from('analyses')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
    
    analyses.value = analyses.value.filter(a => a.id !== id);
  } catch (error) {
    console.error('Error deleting analysis:', error);
    alert('Failed to delete analysis. Please try again.');
  } finally {
    deleting.value = null;
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(date);
};

const getScoreColor = (score) => {
  if (score >= 8) return 'text-success-500 bg-success-500/10';
  if (score >= 6) return 'text-warning-500 bg-warning-500/10';
  if (score >= 4) return 'text-primary-500 bg-primary-500/10';
  return 'text-error-500 bg-error-500/10';
};

const getScoreLabel = (score) => {
  if (score >= 8) return 'Excellent';
  if (score >= 6) return 'Good';
  if (score >= 4) return 'Average';
  return 'Needs Work';
};
</script>

<template>
  <div class="max-w-6xl mx-auto">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Analysis History</h1>
      <p class="text-gray-600 mt-1">View and manage all your landing page analyses</p>
    </div>
    
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search analyses..."
                class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            <select 
              v-model="scoreFilter"
              class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">All Scores</option>
              <option value="excellent">8-10 (Excellent)</option>
              <option value="good">6-7 (Good)</option>
              <option value="average">4-5 (Average)</option>
              <option value="poor">1-3 (Needs Work)</option>
            </select>
          </div>
          <a 
            href="/dashboard/analysis/new" 
            class="bg-primary-500 text-white px-4 py-2 rounded hover:bg-primary-600 transition-colors"
          >
            New Analysis
          </a>
        </div>
      </div>
      
      <div v-if="loading" class="p-6">
        <div class="animate-pulse space-y-4">
          <div v-for="i in 5" :key="i" class="flex items-center space-x-4">
            <div class="h-4 bg-gray-200 rounded w-1/3"></div>
            <div class="h-4 bg-gray-200 rounded w-16"></div>
            <div class="h-4 bg-gray-200 rounded w-20"></div>
            <div class="h-4 bg-gray-200 rounded w-24"></div>
          </div>
        </div>
      </div>
      
      <div v-else-if="filteredAnalyses.length === 0" class="p-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No analyses found</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ searchQuery || scoreFilter !== 'all' ? 'Try adjusting your filters' : 'Get started by analyzing your first landing page' }}
        </p>
        <div class="mt-6">
          <a 
            href="/dashboard/analysis/new"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-500 hover:bg-primary-600"
          >
            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            New Analysis
          </a>
        </div>
      </div>
      
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Page
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Score
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Conversion Rate
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="analysis in filteredAnalyses" :key="analysis.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div>
                    <div class="text-sm font-medium text-gray-900 truncate max-w-xs">
                      {{ analysis.title }}
                    </div>
                    <div class="text-sm text-gray-500 truncate max-w-xs">
                      {{ analysis.url }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getScoreColor(analysis.score)"
                >
                  {{ analysis.score }}/10 - {{ getScoreLabel(analysis.score) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ analysis.conversion_rate }}%
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(analysis.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <a 
                    :href="`/dashboard/analysis/${analysis.id}`"
                    class="text-primary-600 hover:text-primary-900"
                  >
                    View
                  </a>
                  <button
                    @click="deleteAnalysis(analysis.id)"
                    :disabled="deleting === analysis.id"
                    class="text-error-600 hover:text-error-900 disabled:opacity-50"
                  >
                    {{ deleting === analysis.id ? 'Deleting...' : 'Delete' }}
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>