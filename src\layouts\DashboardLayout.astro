---
import Layout from './Layout.astro';
import Sidebar from '../components/dashboard/Sidebar.vue';
import Navbar from '../components/dashboard/Navbar.vue';

interface Props {
  title: string;
  description?: string;
}

const { title, description } = Astro.props;
---

<Layout title={title} description={description} isAuthenticated={true}>
  <Sidebar client:load />
  
  <div class="flex-1 flex flex-col overflow-hidden">
    <Navbar client:load />
    <main class="flex-1 overflow-y-auto p-6 bg-gray-50">
      <slot />
    </main>
  </div>
</Layout>