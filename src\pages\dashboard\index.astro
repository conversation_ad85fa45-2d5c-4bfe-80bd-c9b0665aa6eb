---
import DashboardLayout from '../../layouts/DashboardLayout.astro';
import AnalysisList from '../../components/dashboard/AnalysisList.vue';
import NewAnalysisCard from '../../components/analysis/NewAnalysisCard.vue';
---

<DashboardLayout title="Dashboard - ConvertIQ">
  <div class="container mx-auto">
    <h1 class="text-2xl font-bold mb-6">Dashboard</h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="lg:col-span-2">
        <h2 class="text-xl font-medium mb-4">Recent Analyses</h2>
        <AnalysisList client:load />
      </div>
      
      <div class="lg:col-span-1">
        <h2 class="text-xl font-medium mb-4">Start New Analysis</h2>
        <NewAnalysisCard client:load />
      </div>
    </div>
  </div>
</DashboardLayout>